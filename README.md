# TikTok Automation App

Ứng dụng tự động hóa tương tác và follow trên TikTok với giao diện desktop.

## Cấu trúc dự án

```
tiktok-automation-app/
├── backend/                   # Node.js backend vớ<PERSON> Playwright
│   ├── data/                 # File input từ người dùng
│   ├── profiles/             # Browser profiles
│   ├── src/                  # Mã nguồn backend
│   └── db.json              # Database chính
│
└── frontend/                 # Tauri + React frontend
    ├── src/                  # Mã nguồn frontend
    ├── src-tauri/           # Cấu hình Tauri
    └── package.json
```

## Cài đặt

### Backend
```bash
cd backend
npm install
```

### Frontend
```bash
cd frontend
npm install
```

## Chạy ứng dụng

### Development
1. Chạy backend:
```bash
cd backend
npm run dev
```

2. Chạy frontend:
```bash
cd frontend
npm run tauri dev
```

### Production
```bash
cd frontend
npm run tauri build
```

## Tính năng

- [x] Cấu trúc dự án cơ bản
- [x] Quản lý tài khoản & dữ liệu đầu vào
- [x] Quản lý phiên đăng nhập (Login automation)
- [x] Giao diện dashboard với real-time updates
- [x] WebSocket communication giữa frontend-backend
- [x] Hệ thống log chi tiết
- [ ] Tương tác & Follow tự động (Follow-Interact automation)
- [x] Cấu hình & quản lý giới hạn
- [x] Theo dõi & vận hành

## Hướng dẫn sử dụng

### 1. Chuẩn bị dữ liệu

Tạo các file dữ liệu trong thư mục `backend/data/`:

**accounts.txt** - Danh sách tài khoản (format: username:password)
```
user1:password1
user2:password2
```

**proxies.txt** - Danh sách proxy (format: ip:port hoặc ip:port:user:pass)
```
*************:8080
*************:8080:username:password
```

**comments.txt** - Danh sách bình luận
```
Nice video!
Great content!
Love this!
```

### 2. Chạy ứng dụng

1. **Khởi động Backend:**
```bash
cd backend
npm start
```

2. **Khởi động Frontend:**
```bash
cd frontend
npm run dev
```

3. **Mở ứng dụng:** http://localhost:1420

### 3. Sử dụng giao diện

1. **Nạp dữ liệu:** Sử dụng các nút "Nạp Tài khoản", "Nạp Proxy", "Nạp Bình luận"
2. **Đăng nhập:** Click "Đăng nhập" trên từng tài khoản hoặc chọn nhiều tài khoản để đăng nhập hàng loạt
3. **Giải CAPTCHA:** Khi gặp CAPTCHA, browser sẽ hiển thị để bạn giải quyết thủ công
4. **Theo dõi log:** Xem chi tiết hoạt động trong cửa sổ Log

### 4. Trạng thái tài khoản

- **Chưa đăng nhập:** Tài khoản mới được nạp
- **Đang đăng nhập:** Đang trong quá trình đăng nhập
- **Cần giải CAPTCHA:** Cần giải quyết CAPTCHA thủ công
- **Sẵn sàng:** Đã đăng nhập thành công
- **Lỗi:** Gặp lỗi trong quá trình xử lý

## Công nghệ sử dụng

- **Backend**: Node.js, Playwright, WebSocket
- **Frontend**: Tauri, React, Vite
- **Database**: JSON file
