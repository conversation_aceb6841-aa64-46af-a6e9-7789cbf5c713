class WebSocketClient {
  constructor(url = 'ws://localhost:8080') {
    this.url = url;
    this.ws = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
    this.eventHandlers = new Map();
    this.messageQueue = [];
  }

  /**
   * <PERSON><PERSON>t nối đến WebSocket server
   */
  connect() {
    return new Promise((resolve, reject) => {
      try {
        this.ws = new WebSocket(this.url);

        this.ws.onopen = () => {
          console.log('Connected to backend server');
          this.isConnected = true;
          this.reconnectAttempts = 0;

          // G<PERSON>i các tin nhắn đang chờ trong queue
          this.flushMessageQueue();

          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error('Error parsing message:', error);
          }
        };

        this.ws.onclose = () => {
          console.log('Disconnected from backend server');
          this.isConnected = false;
          this.attemptReconnect();
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket error:', error);
          if (!this.isConnected) {
            reject(error);
          }
        };

      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Xử lý tin nhắn từ server
   */
  handleMessage(message) {
    const { type } = message;

    // Gọi handler tương ứng nếu có
    if (this.eventHandlers.has(type)) {
      const handlers = this.eventHandlers.get(type);
      handlers.forEach(handler => {
        try {
          handler(message);
        } catch (error) {
          console.error(`Error in ${type} handler:`, error);
        }
      });
    }

    // Log tin nhắn để debug
    console.log('Received message:', message);
  }

  /**
   * Đăng ký handler cho loại event
   */
  on(eventType, handler) {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, []);
    }
    this.eventHandlers.get(eventType).push(handler);
  }

  /**
   * Hủy đăng ký handler
   */
  off(eventType, handler) {
    if (this.eventHandlers.has(eventType)) {
      const handlers = this.eventHandlers.get(eventType);
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  /**
   * Gửi command đến server
   */
  sendCommand(command, data = null) {
    const message = {
      command,
      data,
      timestamp: new Date().toISOString()
    };

    if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      // Thêm vào queue nếu chưa kết nối
      this.messageQueue.push(message);
    }
  }

  /**
   * Gửi các tin nhắn trong queue
   */
  flushMessageQueue() {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      this.ws.send(JSON.stringify(message));
    }
  }

  /**
   * Thử kết nối lại
   */
  attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

      setTimeout(() => {
        this.connect().catch(error => {
          console.error('Reconnection failed:', error);
        });
      }, this.reconnectDelay * this.reconnectAttempts);
    } else {
      console.error('Max reconnection attempts reached');
    }
  }

  /**
   * Ngắt kết nối
   */
  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
      this.isConnected = false;
    }
  }

  /**
   * Kiểm tra trạng thái kết nối
   */
  getConnectionStatus() {
    return this.isConnected;
  }

  // Các method tiện ích cho các command cụ thể
  loadAccounts(filePath = null) {
    this.sendCommand('load_accounts', { filePath });
  }

  loadProxies(filePath = null) {
    this.sendCommand('load_proxies', { filePath });
  }

  loadComments(filePath = null) {
    this.sendCommand('load_comments', { filePath });
  }

  getAccounts() {
    this.sendCommand('get_accounts');
  }

  getSettings() {
    this.sendCommand('get_settings');
  }

  updateSettings(settings) {
    this.sendCommand('update_settings', { settings });
  }

  startAutomation() {
    this.sendCommand('start_automation');
  }

  stopAutomation() {
    this.sendCommand('stop_automation');
  }
}

export default WebSocketClient;
