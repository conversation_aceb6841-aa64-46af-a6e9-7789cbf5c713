const path = require('path');
const DatabaseManager = require('./database/manager');
const WebSocketServer = require('./server/websocket');
const {
  readAccountsFile,
  readProxiesFile,
  readCommentsFile,
  formatTime
} = require('./utils');

class TikTokAutomationBackend {
  constructor() {
    this.db = new DatabaseManager();
    this.wsServer = new WebSocketServer(8080);
    this.isRunning = false;
  }

  /**
   * Khởi tạo backend
   */
  async initialize() {
    console.log(`[${formatTime()}] TikTok Automation Backend starting...`);

    try {
      // Khởi tạo database
      await this.db.initialize();
      console.log(`[${formatTime()}] Database initialized`);

      // Khởi tạo WebSocket server
      this.wsServer.start();

      // Đăng ký các command handlers
      this.registerCommandHandlers();

      this.isRunning = true;
      console.log(`[${formatTime()}] Backend started successfully`);

    } catch (error) {
      console.error(`[${formatTime()}] Failed to start backend:`, error);
      process.exit(1);
    }
  }

  /**
   * Đăng ký các command handlers cho WebSocket
   */
  registerCommandHandlers() {
    // Command: Load accounts từ file
    this.wsServer.onCommand('load_accounts', async (ws, data) => {
      try {
        const filePath = data.filePath || path.join(__dirname, '../data/accounts.txt');
        const accounts = await readAccountsFile(filePath);

        if (accounts.length > 0) {
          await this.db.addAccounts(accounts);
          this.wsServer.sendSuccess(ws, `Loaded ${accounts.length} accounts successfully`);
          this.wsServer.sendLog('info', `Loaded ${accounts.length} accounts from file`);
        } else {
          this.wsServer.sendError(ws, 'No valid accounts found in file');
        }
      } catch (error) {
        this.wsServer.sendError(ws, `Failed to load accounts: ${error.message}`);
      }
    });

    // Command: Load proxies từ file
    this.wsServer.onCommand('load_proxies', async (ws, data) => {
      try {
        const filePath = data.filePath || path.join(__dirname, '../data/proxies.txt');
        const proxies = await readProxiesFile(filePath);

        if (proxies.length > 0) {
          await this.db.addProxies(proxies);
          this.wsServer.sendSuccess(ws, `Loaded ${proxies.length} proxies successfully`);
          this.wsServer.sendLog('info', `Loaded ${proxies.length} proxies from file`);
        } else {
          this.wsServer.sendError(ws, 'No valid proxies found in file');
        }
      } catch (error) {
        this.wsServer.sendError(ws, `Failed to load proxies: ${error.message}`);
      }
    });

    // Command: Load comments từ file
    this.wsServer.onCommand('load_comments', async (ws, data) => {
      try {
        const filePath = data.filePath || path.join(__dirname, '../data/comments.txt');
        const comments = await readCommentsFile(filePath);

        if (comments.length > 0) {
          await this.db.addComments(comments);
          this.wsServer.sendSuccess(ws, `Loaded ${comments.length} comments successfully`);
          this.wsServer.sendLog('info', `Loaded ${comments.length} comments from file`);
        } else {
          this.wsServer.sendError(ws, 'No valid comments found in file');
        }
      } catch (error) {
        this.wsServer.sendError(ws, `Failed to load comments: ${error.message}`);
      }
    });

    // Command: Get accounts
    this.wsServer.onCommand('get_accounts', async (ws, data) => {
      try {
        const accounts = await this.db.getAccounts();
        this.wsServer.sendSuccess(ws, 'Accounts retrieved successfully', { accounts });
      } catch (error) {
        this.wsServer.sendError(ws, `Failed to get accounts: ${error.message}`);
      }
    });

    // Command: Get settings
    this.wsServer.onCommand('get_settings', async (ws, data) => {
      try {
        const settings = await this.db.getSettings();
        this.wsServer.sendSuccess(ws, 'Settings retrieved successfully', { settings });
      } catch (error) {
        this.wsServer.sendError(ws, `Failed to get settings: ${error.message}`);
      }
    });

    // Command: Update settings
    this.wsServer.onCommand('update_settings', async (ws, data) => {
      try {
        const settings = await this.db.updateSettings(data.settings);
        this.wsServer.sendSuccess(ws, 'Settings updated successfully', { settings });
        this.wsServer.sendLog('info', 'Settings updated');
      } catch (error) {
        this.wsServer.sendError(ws, `Failed to update settings: ${error.message}`);
      }
    });

    // Command: Start automation (placeholder)
    this.wsServer.onCommand('start_automation', async (ws, data) => {
      try {
        this.wsServer.sendLog('info', 'Starting automation...');
        // TODO: Implement automation logic
        this.wsServer.sendSuccess(ws, 'Automation started (placeholder)');
      } catch (error) {
        this.wsServer.sendError(ws, `Failed to start automation: ${error.message}`);
      }
    });

    // Command: Stop automation (placeholder)
    this.wsServer.onCommand('stop_automation', async (ws, data) => {
      try {
        this.wsServer.sendLog('info', 'Stopping automation...');
        // TODO: Implement stop logic
        this.wsServer.sendSuccess(ws, 'Automation stopped (placeholder)');
      } catch (error) {
        this.wsServer.sendError(ws, `Failed to stop automation: ${error.message}`);
      }
    });

    console.log(`[${formatTime()}] Command handlers registered`);
  }

  /**
   * Dừng backend
   */
  async shutdown() {
    console.log(`[${formatTime()}] Shutting down backend...`);
    this.wsServer.stop();
    this.isRunning = false;
  }
}

// Khởi tạo và chạy backend
const backend = new TikTokAutomationBackend();

// Xử lý tín hiệu tắt ứng dụng
process.on('SIGINT', async () => {
  console.log('\nReceived SIGINT, shutting down gracefully...');
  await backend.shutdown();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\nReceived SIGTERM, shutting down gracefully...');
  await backend.shutdown();
  process.exit(0);
});

// Bắt đầu backend
backend.initialize().catch(error => {
  console.error('Failed to initialize backend:', error);
  process.exit(1);
});
